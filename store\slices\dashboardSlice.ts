import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { DashboardStats, DashboardState } from '../../types';

export const fetchDashboardStats = createAsyncThunk(
  'dashboard/fetchDashboardStats',
  async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock dashboard stats
    const mockStats: DashboardStats = {
      today: {
        orders: 23,
        revenue: 847.50,
        averageOrderValue: 36.85
      },
      thisWeek: {
        orders: 156,
        revenue: 5234.75,
        averageOrderValue: 33.56
      },
      thisMonth: {
        orders: 678,
        revenue: 22456.80,
        averageOrderValue: 33.12
      },
      pendingOrders: 5,
      preparingOrders: 8,
      readyOrders: 3
    };
    
    return mockStats;
  }
);

const initialState: DashboardState = {
  stats: null,
  loading: false,
  error: null
};

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateOrderCounts: (state, action) => {
      if (state.stats) {
        const { pendingOrders, preparingOrders, readyOrders } = action.payload;
        state.stats.pendingOrders = pendingOrders;
        state.stats.preparingOrders = preparingOrders;
        state.stats.readyOrders = readyOrders;
      }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDashboardStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.loading = false;
        state.stats = action.payload;
      })
      .addCase(fetchDashboardStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch dashboard stats';
      });
  }
});

export const { clearError, updateOrderCounts } = dashboardSlice.actions;
export default dashboardSlice.reducer;
