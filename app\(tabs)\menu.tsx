import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchMenuItems, updateMenuItemAvailability } from '@/store/slices/menuSlice';
import { MenuItem } from '@/types';
import React, { useEffect, useState } from 'react';
import { FlatList, RefreshControl, StyleSheet, Switch, Text, TouchableOpacity, View } from 'react-native';

export default function MenuScreen() {
  const dispatch = useAppDispatch();
  const { categories, items, loading } = useAppSelector(state => state.menu);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    dispatch(fetchMenuItems());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchMenuItems());
  };

  const handleToggleAvailability = (itemId: string, isAvailable: boolean) => {
    dispatch(updateMenuItemAvailability({ itemId, isAvailable }));
  };

  const filteredItems = items.filter(item => 
    selectedCategory === 'all' || item.category === selectedCategory
  );

  const renderMenuItem = ({ item }: { item: MenuItem }) => (
    <View style={styles.menuItemCard}>
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <Text style={styles.itemName}>{item.name}</Text>
          <Text style={styles.itemDescription} numberOfLines={2}>
            {item.description}
          </Text>
          <View style={styles.itemMeta}>
            <Text style={styles.itemPrice}>${item.price.toFixed(2)}</Text>
            <Text style={styles.prepTime}>{item.preparationTime} min</Text>
          </View>
        </View>
        
        <View style={styles.itemActions}>
          <View style={styles.availabilityToggle}>
            <Text style={[styles.availabilityText, { color: item.isAvailable ? Colors.light.success : Colors.light.error }]}>
              {item.isAvailable ? 'Available' : 'Unavailable'}
            </Text>
            <Switch
              value={item.isAvailable}
              onValueChange={(value) => handleToggleAvailability(item.id, value)}
              trackColor={{ false: Colors.light.border, true: Colors.light.success }}
              thumbColor={item.isAvailable ? Colors.light.textInverse : Colors.light.textTertiary}
            />
          </View>
        </View>
      </View>

      {/* Item Tags */}
      <View style={styles.itemTags}>
        {item.isVegetarian && (
          <View style={[styles.tag, { backgroundColor: Colors.light.success }]}>
            <Text style={styles.tagText}>Vegetarian</Text>
          </View>
        )}
        {item.isVegan && (
          <View style={[styles.tag, { backgroundColor: Colors.light.secondary }]}>
            <Text style={styles.tagText}>Vegan</Text>
          </View>
        )}
        {item.isGlutenFree && (
          <View style={[styles.tag, { backgroundColor: Colors.light.warning }]}>
            <Text style={styles.tagText}>Gluten Free</Text>
          </View>
        )}
      </View>

      {/* Customizations */}
      {item.customizations && item.customizations.length > 0 && (
        <View style={styles.customizationsContainer}>
          <Text style={styles.customizationsTitle}>Customizations:</Text>
          {item.customizations.map((customization, index) => (
            <Text key={index} style={styles.customizationText}>
              • {customization.name} ({customization.options.length} options)
            </Text>
          ))}
        </View>
      )}
    </View>
  );

  const categoryOptions = [
    { key: 'all', label: 'All Items' },
    ...categories.map(cat => ({ key: cat.name, label: cat.name }))
  ];

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Menu Management</Text>
        <TouchableOpacity style={styles.addButton}>
          <IconSymbol name="plus" size={20} color={Colors.light.textInverse} />
          <Text style={styles.addButtonText}>Add Item</Text>
        </TouchableOpacity>
      </View>

      {/* Category Filter */}
      <View style={styles.filterContainer}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={categoryOptions}
          keyExtractor={(item) => item.key}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterTab,
                selectedCategory === item.key && styles.activeFilterTab
              ]}
              onPress={() => setSelectedCategory(item.key)}
            >
              <Text style={[
                styles.filterTabText,
                selectedCategory === item.key && styles.activeFilterTabText
              ]}>
                {item.label}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>

      {/* Menu Items List */}
      <FlatList
        data={filteredItems}
        keyExtractor={(item) => item.id}
        renderItem={renderMenuItem}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={handleRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <IconSymbol name="book" size={64} color={Colors.light.textTertiary} />
            <Text style={styles.emptyText}>No menu items found</Text>
            <Text style={styles.emptySubtext}>
              {selectedCategory === 'all' 
                ? 'Add your first menu item to get started'
                : `No items in ${selectedCategory} category`
              }
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  addButtonText: {
    color: Colors.light.textInverse,
    fontSize: 14,
    fontWeight: '600',
  },
  filterContainer: {
    backgroundColor: Colors.light.surface,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  activeFilterTab: {
    backgroundColor: Colors.light.primary,
  },
  filterTabText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: Colors.light.textInverse,
  },
  listContainer: {
    padding: 16,
  },
  menuItemCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  itemInfo: {
    flex: 1,
    marginRight: 16,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  itemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  itemPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  prepTime: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    backgroundColor: Colors.light.backgroundSecondary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  itemActions: {
    alignItems: 'flex-end',
  },
  availabilityToggle: {
    alignItems: 'center',
    gap: 4,
  },
  availabilityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  itemTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 8,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  customizationsContainer: {
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 8,
    borderRadius: 8,
  },
  customizationsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  customizationText: {
    fontSize: 11,
    color: Colors.light.textTertiary,
    marginBottom: 2,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});
