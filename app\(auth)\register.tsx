import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { clearError, registerUser } from '@/store/slices/authSlice';
import { RegisterData } from '@/types';
import { Link, router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';

export default function RegisterScreen() {
  const dispatch = useAppDispatch();
  const { isLoading, error, isAuthenticated } = useAppSelector(state => state.auth);
  
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<RegisterData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    restaurantName: '',
    restaurantType: 'American',
    description: '',
    address: {
      id: '',
      street: '',
      city: '',
      state: '',
      zipCode: ''
    },
    businessLicense: '',
    taxId: '',
    acceptTerms: false,
    acceptPrivacy: false,
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});

  // Navigate to main app if authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const validateStep1 = () => {
    const errors: {[key: string]: string} = {};
    
    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }
    
    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email';
    }
    
    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }
    
    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
    
    if (!formData.phone.trim()) {
      errors.phone = 'Phone number is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateStep2 = () => {
    const errors: {[key: string]: string} = {};
    
    if (!formData.restaurantName.trim()) {
      errors.restaurantName = 'Restaurant name is required';
    }
    
    if (!formData.description.trim()) {
      errors.description = 'Restaurant description is required';
    }
    
    if (!formData.address.street.trim()) {
      errors.street = 'Street address is required';
    }
    
    if (!formData.address.city.trim()) {
      errors.city = 'City is required';
    }
    
    if (!formData.address.state.trim()) {
      errors.state = 'State is required';
    }
    
    if (!formData.address.zipCode.trim()) {
      errors.zipCode = 'ZIP code is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateStep3 = () => {
    const errors: {[key: string]: string} = {};
    
    if (!formData.acceptTerms) {
      errors.acceptTerms = 'You must accept the Terms of Service';
    }
    
    if (!formData.acceptPrivacy) {
      errors.acceptPrivacy = 'You must accept the Privacy Policy';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    let isValid = false;
    
    if (currentStep === 1) {
      isValid = validateStep1();
    } else if (currentStep === 2) {
      isValid = validateStep2();
    }
    
    if (isValid && currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setFormErrors({});
    }
  };

  const handleRegister = async () => {
    if (!validateStep3()) return;
    
    try {
      await dispatch(registerUser(formData)).unwrap();
      // Navigation handled by useEffect
    } catch (error) {
      // Error handled by Redux state
    }
  };

  const updateField = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev as any)[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Personal Information</Text>
      
      <View style={styles.row}>
        <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
          <Text style={styles.label}>First Name</Text>
          <TextInput
            style={[styles.input, formErrors.firstName && styles.inputError]}
            placeholder="John"
            value={formData.firstName}
            onChangeText={(value) => updateField('firstName', value)}
            autoCapitalize="words"
            editable={!isLoading}
          />
          {formErrors.firstName && <Text style={styles.errorText}>{formErrors.firstName}</Text>}
        </View>
        
        <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
          <Text style={styles.label}>Last Name</Text>
          <TextInput
            style={[styles.input, formErrors.lastName && styles.inputError]}
            placeholder="Doe"
            value={formData.lastName}
            onChangeText={(value) => updateField('lastName', value)}
            autoCapitalize="words"
            editable={!isLoading}
          />
          {formErrors.lastName && <Text style={styles.errorText}>{formErrors.lastName}</Text>}
        </View>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Email Address</Text>
        <TextInput
          style={[styles.input, formErrors.email && styles.inputError]}
          placeholder="<EMAIL>"
          value={formData.email}
          onChangeText={(value) => updateField('email', value)}
          keyboardType="email-address"
          autoCapitalize="none"
          autoCorrect={false}
          editable={!isLoading}
        />
        {formErrors.email && <Text style={styles.errorText}>{formErrors.email}</Text>}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Phone Number</Text>
        <TextInput
          style={[styles.input, formErrors.phone && styles.inputError]}
          placeholder="+****************"
          value={formData.phone}
          onChangeText={(value) => updateField('phone', value)}
          keyboardType="phone-pad"
          editable={!isLoading}
        />
        {formErrors.phone && <Text style={styles.errorText}>{formErrors.phone}</Text>}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Password</Text>
        <View style={styles.passwordContainer}>
          <TextInput
            style={[styles.passwordInput, formErrors.password && styles.inputError]}
            placeholder="Create a strong password"
            value={formData.password}
            onChangeText={(value) => updateField('password', value)}
            secureTextEntry={!showPassword}
            autoCapitalize="none"
            autoCorrect={false}
            editable={!isLoading}
          />
          <TouchableOpacity
            onPress={() => setShowPassword(!showPassword)}
            style={styles.eyeButton}
          >
            <IconSymbol 
              name={showPassword ? "eye.slash" : "eye"} 
              size={20} 
              color={Colors.light.textSecondary} 
            />
          </TouchableOpacity>
        </View>
        {formErrors.password && <Text style={styles.errorText}>{formErrors.password}</Text>}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Confirm Password</Text>
        <View style={styles.passwordContainer}>
          <TextInput
            style={[styles.passwordInput, formErrors.confirmPassword && styles.inputError]}
            placeholder="Confirm your password"
            value={formData.confirmPassword}
            onChangeText={(value) => updateField('confirmPassword', value)}
            secureTextEntry={!showConfirmPassword}
            autoCapitalize="none"
            autoCorrect={false}
            editable={!isLoading}
          />
          <TouchableOpacity
            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            style={styles.eyeButton}
          >
            <IconSymbol 
              name={showConfirmPassword ? "eye.slash" : "eye"} 
              size={20} 
              color={Colors.light.textSecondary} 
            />
          </TouchableOpacity>
        </View>
        {formErrors.confirmPassword && <Text style={styles.errorText}>{formErrors.confirmPassword}</Text>}
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Restaurant Information</Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Restaurant Name</Text>
        <TextInput
          style={[styles.input, formErrors.restaurantName && styles.inputError]}
          placeholder="Your Restaurant Name"
          value={formData.restaurantName}
          onChangeText={(value) => updateField('restaurantName', value)}
          editable={!isLoading}
        />
        {formErrors.restaurantName && <Text style={styles.errorText}>{formErrors.restaurantName}</Text>}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Restaurant Type</Text>
        <TextInput
          style={styles.input}
          placeholder="e.g., Italian, Chinese, American"
          value={formData.restaurantType}
          onChangeText={(value) => updateField('restaurantType', value)}
          editable={!isLoading}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Description</Text>
        <TextInput
          style={[styles.textArea, formErrors.description && styles.inputError]}
          placeholder="Tell customers about your restaurant..."
          value={formData.description}
          onChangeText={(value) => updateField('description', value)}
          multiline
          numberOfLines={3}
          editable={!isLoading}
        />
        {formErrors.description && <Text style={styles.errorText}>{formErrors.description}</Text>}
      </View>

      <Text style={styles.sectionTitle}>Address</Text>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>Street Address</Text>
        <TextInput
          style={[styles.input, formErrors.street && styles.inputError]}
          placeholder="123 Main Street"
          value={formData.address.street}
          onChangeText={(value) => updateField('address.street', value)}
          editable={!isLoading}
        />
        {formErrors.street && <Text style={styles.errorText}>{formErrors.street}</Text>}
      </View>

      <View style={styles.row}>
        <View style={[styles.inputContainer, { flex: 2, marginRight: 8 }]}>
          <Text style={styles.label}>City</Text>
          <TextInput
            style={[styles.input, formErrors.city && styles.inputError]}
            placeholder="City"
            value={formData.address.city}
            onChangeText={(value) => updateField('address.city', value)}
            editable={!isLoading}
          />
          {formErrors.city && <Text style={styles.errorText}>{formErrors.city}</Text>}
        </View>

        <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
          <Text style={styles.label}>State</Text>
          <TextInput
            style={[styles.input, formErrors.state && styles.inputError]}
            placeholder="State"
            value={formData.address.state}
            onChangeText={(value) => updateField('address.state', value)}
            editable={!isLoading}
          />
          {formErrors.state && <Text style={styles.errorText}>{formErrors.state}</Text>}
        </View>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.label}>ZIP Code</Text>
        <TextInput
          style={[styles.input, formErrors.zipCode && styles.inputError]}
          placeholder="12345"
          value={formData.address.zipCode}
          onChangeText={(value) => updateField('address.zipCode', value)}
          keyboardType="numeric"
          editable={!isLoading}
        />
        {formErrors.zipCode && <Text style={styles.errorText}>{formErrors.zipCode}</Text>}
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Terms & Conditions</Text>

      <View style={styles.checkboxContainer}>
        <Switch
          value={formData.acceptTerms}
          onValueChange={(value) => updateField('acceptTerms', value)}
          trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
          thumbColor={Colors.light.surface}
        />
        <Text style={styles.checkboxText}>
          I accept the <Text style={styles.linkText}>Terms of Service</Text>
        </Text>
      </View>
      {formErrors.acceptTerms && <Text style={styles.errorText}>{formErrors.acceptTerms}</Text>}

      <View style={styles.checkboxContainer}>
        <Switch
          value={formData.acceptPrivacy}
          onValueChange={(value) => updateField('acceptPrivacy', value)}
          trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
          thumbColor={Colors.light.surface}
        />
        <Text style={styles.checkboxText}>
          I accept the <Text style={styles.linkText}>Privacy Policy</Text>
        </Text>
      </View>
      {formErrors.acceptPrivacy && <Text style={styles.errorText}>{formErrors.acceptPrivacy}</Text>}

      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Account Summary</Text>
        <Text style={styles.summaryText}>Name: {formData.firstName} {formData.lastName}</Text>
        <Text style={styles.summaryText}>Email: {formData.email}</Text>
        <Text style={styles.summaryText}>Restaurant: {formData.restaurantName}</Text>
        <Text style={styles.summaryText}>Type: {formData.restaurantType}</Text>
      </View>
    </View>
  );

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <IconSymbol name="fork.knife" size={48} color={Colors.light.primary} />
          </View>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>Join our restaurant management platform</Text>
        </View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          {[1, 2, 3].map((step) => (
            <View key={step} style={styles.progressStep}>
              <View style={[
                styles.progressCircle,
                currentStep >= step && styles.progressCircleActive
              ]}>
                <Text style={[
                  styles.progressText,
                  currentStep >= step && styles.progressTextActive
                ]}>
                  {step}
                </Text>
              </View>
              {step < 3 && (
                <View style={[
                  styles.progressLine,
                  currentStep > step && styles.progressLineActive
                ]} />
              )}
            </View>
          ))}
        </View>

        {/* Form Steps */}
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}

        {/* Error Message */}
        {error && (
          <View style={styles.errorContainer}>
            <IconSymbol name="exclamationmark.triangle" size={16} color={Colors.light.error} />
            <Text style={styles.errorMessage}>{error}</Text>
          </View>
        )}

        {/* Navigation Buttons */}
        <View style={styles.buttonContainer}>
          {currentStep > 1 && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBack}
              disabled={isLoading}
            >
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              currentStep === 1 && styles.nextButtonFull,
              isLoading && styles.nextButtonDisabled
            ]}
            onPress={currentStep === 3 ? handleRegister : handleNext}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color={Colors.light.textInverse} size="small" />
            ) : (
              <Text style={styles.nextButtonText}>
                {currentStep === 3 ? 'Create Account' : 'Next'}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>Already have an account? </Text>
          <Link href="/(auth)/login" asChild>
            <TouchableOpacity>
              <Text style={styles.signInText}>Sign In</Text>
            </TouchableOpacity>
          </Link>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressCircleActive: {
    backgroundColor: Colors.light.primary,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },
  progressTextActive: {
    color: Colors.light.textInverse,
  },
  progressLine: {
    width: 40,
    height: 2,
    backgroundColor: Colors.light.border,
    marginHorizontal: 8,
  },
  progressLineActive: {
    backgroundColor: Colors.light.primary,
  },
  stepContainer: {
    marginBottom: 30,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 20,
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 6,
  },
  input: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  inputError: {
    borderColor: Colors.light.error,
  },
  textArea: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
    height: 80,
    textAlignVertical: 'top',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  eyeButton: {
    padding: 12,
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.error,
    marginTop: 4,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkboxText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 12,
    flex: 1,
  },
  linkText: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  summaryContainer: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  summaryText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorMessage: {
    fontSize: 14,
    color: Colors.light.error,
    marginLeft: 8,
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginBottom: 30,
  },
  backButton: {
    flex: 1,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  nextButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonFull: {
    flex: 1,
    marginRight: 0,
  },
  nextButtonDisabled: {
    opacity: 0.6,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  signInText: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '600',
  },
});
