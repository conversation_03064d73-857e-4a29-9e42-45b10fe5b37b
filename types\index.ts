// Order Status Types
export type OrderStatus = 
  | 'pending'      // New order waiting for restaurant confirmation
  | 'confirmed'    // Restaurant confirmed the order
  | 'preparing'    // Restaurant is preparing the order
  | 'ready'        // Order is ready for pickup/delivery
  | 'picked_up'    // Order picked up by delivery rider
  | 'delivered'    // Order delivered to customer
  | 'cancelled'    // Order cancelled
  | 'rejected';    // Order rejected by restaurant

// Payment Status Types
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

// Order Type
export type OrderType = 'delivery' | 'pickup';

// Customer Information
export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  address?: Address;
}

// Address Information
export interface Address {
  id: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  instructions?: string;
}

// Menu Item
export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  isAvailable: boolean;
  preparationTime: number; // in minutes
  ingredients?: string[];
  allergens?: string[];
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  customizations?: MenuCustomization[];
}

// Menu Customization Options
export interface MenuCustomization {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  options: CustomizationOption[];
}

export interface CustomizationOption {
  id: string;
  name: string;
  price: number;
}

// Order Item
export interface OrderItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  customizations: SelectedCustomization[];
  specialInstructions?: string;
  itemTotal: number;
}

export interface SelectedCustomization {
  customizationId: string;
  customizationName: string;
  selectedOptions: CustomizationOption[];
}

// Main Order Interface
export interface Order {
  id: string;
  orderNumber: string;
  customer: Customer;
  items: OrderItem[];
  orderType: OrderType;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  
  // Pricing
  subtotal: number;
  tax: number;
  deliveryFee: number;
  tip: number;
  discount: number;
  total: number;
  
  // Timing
  orderTime: string; // ISO string
  estimatedPreparationTime: number; // in minutes
  estimatedDeliveryTime?: string; // ISO string
  confirmedTime?: string; // ISO string
  readyTime?: string; // ISO string
  deliveredTime?: string; // ISO string
  
  // Delivery Information
  deliveryAddress?: Address;
  deliveryInstructions?: string;
  riderId?: string;
  riderName?: string;
  riderPhone?: string;
  
  // Additional Information
  specialInstructions?: string;
  paymentMethod: string;
  restaurantNotes?: string;
}

// Restaurant Information
export interface Restaurant {
  id: string;
  name: string;
  description: string;
  address: Address;
  phone: string;
  email: string;
  website?: string;
  logo?: string;
  coverImage?: string;
  
  // Operating Hours
  operatingHours: OperatingHours[];
  
  // Settings
  isOpen: boolean;
  acceptingOrders: boolean;
  averagePreparationTime: number;
  minimumOrderAmount: number;
  deliveryRadius: number; // in kilometers
  deliveryFee: number;
  
  // Statistics
  totalOrders: number;
  rating: number;
  reviewCount: number;
}

export interface OperatingHours {
  dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  openTime: string; // "09:00"
  closeTime: string; // "22:00"
  isClosed: boolean;
}

// Menu Category
export interface MenuCategory {
  id: string;
  name: string;
  description?: string;
  image?: string;
  sortOrder: number;
  isActive: boolean;
}

// Dashboard Statistics
export interface DashboardStats {
  today: {
    orders: number;
    revenue: number;
    averageOrderValue: number;
  };
  thisWeek: {
    orders: number;
    revenue: number;
    averageOrderValue: number;
  };
  thisMonth: {
    orders: number;
    revenue: number;
    averageOrderValue: number;
  };
  pendingOrders: number;
  preparingOrders: number;
  readyOrders: number;
}

// Notification Types
export interface Notification {
  id: string;
  type: 'new_order' | 'order_update' | 'system' | 'promotion';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  orderId?: string;
  data?: any;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Filter and Sort Options
export interface OrderFilters {
  status?: OrderStatus[];
  orderType?: OrderType[];
  dateFrom?: Date;
  dateTo?: Date;
  minAmount?: number;
  maxAmount?: number;
}

export interface OrderSortOptions {
  field: 'orderTime' | 'total' | 'status';
  direction: 'asc' | 'desc';
}

// Redux State Types
export interface RootState {
  orders: OrdersState;
  restaurant: RestaurantState;
  menu: MenuState;
  dashboard: DashboardState;
  notifications: NotificationsState;
}

export interface OrdersState {
  orders: Order[];
  loading: boolean;
  error: string | null;
  filters: OrderFilters;
  sortOptions: OrderSortOptions;
}

export interface RestaurantState {
  restaurant: Restaurant | null;
  loading: boolean;
  error: string | null;
}

export interface MenuState {
  categories: MenuCategory[];
  items: MenuItem[];
  loading: boolean;
  error: string | null;
}

export interface DashboardState {
  stats: DashboardStats | null;
  loading: boolean;
  error: string | null;
}

export interface NotificationsState {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
}
