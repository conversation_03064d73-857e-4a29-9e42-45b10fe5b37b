import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchDashboardStats } from '@/store/slices/dashboardSlice';
import { fetchRestaurantInfo, updateRestaurantStatus } from '@/store/slices/restaurantSlice';
import React, { useEffect } from 'react';
import { RefreshControl, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function DashboardScreen() {
  const dispatch = useAppDispatch();
  const { stats, loading } = useAppSelector(state => state.dashboard);
  const { restaurant } = useAppSelector(state => state.restaurant);

  useEffect(() => {
    dispatch(fetchDashboardStats());
    dispatch(fetchRestaurantInfo());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchDashboardStats());
    dispatch(fetchRestaurantInfo());
  };

  const toggleRestaurantStatus = () => {
    if (restaurant) {
      dispatch(updateRestaurantStatus({
        isOpen: !restaurant.isOpen,
        acceptingOrders: !restaurant.isOpen
      }));
    }
  };

  const toggleOrderAcceptance = () => {
    if (restaurant) {
      dispatch(updateRestaurantStatus({
        acceptingOrders: !restaurant.acceptingOrders
      }));
    }
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={handleRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.welcomeText}>Welcome back!</Text>
          <Text style={styles.restaurantName}>{restaurant?.name || 'Restaurant'}</Text>
        </View>
        <View style={styles.statusContainer}>
          <TouchableOpacity
            style={[styles.statusButton, restaurant?.isOpen ? styles.openButton : styles.closedButton]}
            onPress={toggleRestaurantStatus}
          >
            <Text style={styles.statusButtonText}>
              {restaurant?.isOpen ? 'OPEN' : 'CLOSED'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={[styles.actionButton, restaurant?.acceptingOrders ? styles.acceptingButton : styles.notAcceptingButton]}
          onPress={toggleOrderAcceptance}
        >
          <IconSymbol
            name={restaurant?.acceptingOrders ? "checkmark.circle.fill" : "pause.circle.fill"}
            size={24}
            color={Colors.light.textInverse}
          />
          <Text style={styles.actionButtonText}>
            {restaurant?.acceptingOrders ? 'Accepting Orders' : 'Paused Orders'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Order Status Cards */}
      <View style={styles.orderStatusGrid}>
        <View style={[styles.statusCard, { backgroundColor: Colors.light.pending }]}>
          <IconSymbol name="clock.fill" size={32} color={Colors.light.textInverse} />
          <Text style={styles.statusCardNumber}>{stats?.pendingOrders || 0}</Text>
          <Text style={styles.statusCardLabel}>Pending</Text>
        </View>

        <View style={[styles.statusCard, { backgroundColor: Colors.light.preparing }]}>
          <IconSymbol name="flame.fill" size={32} color={Colors.light.textInverse} />
          <Text style={styles.statusCardNumber}>{stats?.preparingOrders || 0}</Text>
          <Text style={styles.statusCardLabel}>Preparing</Text>
        </View>

        <View style={[styles.statusCard, { backgroundColor: Colors.light.ready }]}>
          <IconSymbol name="checkmark.circle.fill" size={32} color={Colors.light.textInverse} />
          <Text style={styles.statusCardNumber}>{stats?.readyOrders || 0}</Text>
          <Text style={styles.statusCardLabel}>Ready</Text>
        </View>
      </View>

      {/* Revenue Stats */}
      <View style={styles.revenueSection}>
        <Text style={styles.sectionTitle}>Revenue Overview</Text>

        <View style={styles.revenueCard}>
          <View style={styles.revenueItem}>
            <Text style={styles.revenueLabel}>Today</Text>
            <Text style={styles.revenueAmount}>${stats?.today.revenue.toFixed(2) || '0.00'}</Text>
            <Text style={styles.revenueOrders}>{stats?.today.orders || 0} orders</Text>
          </View>

          <View style={styles.revenueItem}>
            <Text style={styles.revenueLabel}>This Week</Text>
            <Text style={styles.revenueAmount}>${stats?.thisWeek.revenue.toFixed(2) || '0.00'}</Text>
            <Text style={styles.revenueOrders}>{stats?.thisWeek.orders || 0} orders</Text>
          </View>

          <View style={styles.revenueItem}>
            <Text style={styles.revenueLabel}>This Month</Text>
            <Text style={styles.revenueAmount}>${stats?.thisMonth.revenue.toFixed(2) || '0.00'}</Text>
            <Text style={styles.revenueOrders}>{stats?.thisMonth.orders || 0} orders</Text>
          </View>
        </View>
      </View>

      {/* Average Order Value */}
      <View style={styles.avgOrderSection}>
        <Text style={styles.sectionTitle}>Average Order Value</Text>
        <View style={styles.avgOrderCard}>
          <Text style={styles.avgOrderValue}>${stats?.today.averageOrderValue.toFixed(2) || '0.00'}</Text>
          <Text style={styles.avgOrderLabel}>Today's Average</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  welcomeText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  restaurantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  openButton: {
    backgroundColor: Colors.light.success,
  },
  closedButton: {
    backgroundColor: Colors.light.error,
  },
  statusButtonText: {
    color: Colors.light.textInverse,
    fontWeight: 'bold',
    fontSize: 14,
  },
  quickActions: {
    padding: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  acceptingButton: {
    backgroundColor: Colors.light.success,
  },
  notAcceptingButton: {
    backgroundColor: Colors.light.warning,
  },
  actionButtonText: {
    color: Colors.light.textInverse,
    fontSize: 16,
    fontWeight: '600',
  },
  orderStatusGrid: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 24,
  },
  statusCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    gap: 8,
  },
  statusCardNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
  },
  statusCardLabel: {
    fontSize: 12,
    color: Colors.light.textInverse,
    textAlign: 'center',
  },
  revenueSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 16,
  },
  revenueCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  revenueItem: {
    flex: 1,
    alignItems: 'center',
  },
  revenueLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  revenueAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.primary,
    marginBottom: 2,
  },
  revenueOrders: {
    fontSize: 12,
    color: Colors.light.textTertiary,
  },
  avgOrderSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  avgOrderCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avgOrderValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.light.secondary,
    marginBottom: 4,
  },
  avgOrderLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
});
