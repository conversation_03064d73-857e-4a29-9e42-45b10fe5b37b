import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { logoutUser, updateUser } from '@/store/slices/authSlice';
import { updateRestaurant } from '@/store/slices/restaurantSlice';
import React, { useState } from 'react';
import {
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

export default function ProfileScreen() {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { restaurant } = useAppSelector(state => state.restaurant);
  
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
  });
  
  const [editedRestaurant, setEditedRestaurant] = useState({
    name: restaurant?.name || '',
    description: restaurant?.description || '',
    phone: restaurant?.phone || '',
    email: restaurant?.email || '',
    isOpen: restaurant?.isOpen || false,
    acceptingOrders: restaurant?.acceptingOrders || false,
  });

  const handleSave = () => {
    if (user) {
      dispatch(updateUser({
        firstName: editedUser.firstName,
        lastName: editedUser.lastName,
        phone: editedUser.phone,
      }));
    }
    
    if (restaurant) {
      dispatch(updateRestaurant({
        name: editedRestaurant.name,
        description: editedRestaurant.description,
        phone: editedRestaurant.phone,
        email: editedRestaurant.email,
        isOpen: editedRestaurant.isOpen,
        acceptingOrders: editedRestaurant.acceptingOrders,
      }));
    }
    
    setIsEditing(false);
    Alert.alert('Success', 'Profile updated successfully!');
  };

  const handleCancel = () => {
    setEditedUser({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
    });
    setEditedRestaurant({
      name: restaurant?.name || '',
      description: restaurant?.description || '',
      phone: restaurant?.phone || '',
      email: restaurant?.email || '',
      isOpen: restaurant?.isOpen || false,
      acceptingOrders: restaurant?.acceptingOrders || false,
    });
    setIsEditing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: () => dispatch(logoutUser())
        }
      ]
    );
  };

  if (!user || !restaurant) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Profile data not available</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <IconSymbol name="person.fill" size={48} color={Colors.light.textInverse} />
        </View>
        <Text style={styles.userName}>
          {user.firstName} {user.lastName}
        </Text>
        <Text style={styles.userRole}>{user.role.toUpperCase()}</Text>
        
        <View style={styles.headerActions}>
          {isEditing ? (
            <View style={styles.editActions}>
              <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity style={styles.editButton} onPress={() => setIsEditing(true)}>
              <IconSymbol name="pencil" size={16} color={Colors.light.textInverse} />
              <Text style={styles.editButtonText}>Edit Profile</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Personal Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Personal Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>First Name</Text>
          <TextInput
            style={[styles.input, !isEditing && styles.inputDisabled]}
            value={editedUser.firstName}
            onChangeText={(text) => setEditedUser(prev => ({ ...prev, firstName: text }))}
            editable={isEditing}
            placeholder="First Name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Last Name</Text>
          <TextInput
            style={[styles.input, !isEditing && styles.inputDisabled]}
            value={editedUser.lastName}
            onChangeText={(text) => setEditedUser(prev => ({ ...prev, lastName: text }))}
            editable={isEditing}
            placeholder="Last Name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={[styles.input, styles.inputDisabled]}
            value={editedUser.email}
            editable={false}
            placeholder="Email"
          />
          <Text style={styles.helperText}>Email cannot be changed</Text>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Phone</Text>
          <TextInput
            style={[styles.input, !isEditing && styles.inputDisabled]}
            value={editedUser.phone}
            onChangeText={(text) => setEditedUser(prev => ({ ...prev, phone: text }))}
            editable={isEditing}
            placeholder="Phone Number"
            keyboardType="phone-pad"
          />
        </View>
      </View>

      {/* Restaurant Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Restaurant Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Restaurant Name</Text>
          <TextInput
            style={[styles.input, !isEditing && styles.inputDisabled]}
            value={editedRestaurant.name}
            onChangeText={(text) => setEditedRestaurant(prev => ({ ...prev, name: text }))}
            editable={isEditing}
            placeholder="Restaurant Name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.textArea, !isEditing && styles.inputDisabled]}
            value={editedRestaurant.description}
            onChangeText={(text) => setEditedRestaurant(prev => ({ ...prev, description: text }))}
            editable={isEditing}
            placeholder="Restaurant Description"
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Restaurant Phone</Text>
          <TextInput
            style={[styles.input, !isEditing && styles.inputDisabled]}
            value={editedRestaurant.phone}
            onChangeText={(text) => setEditedRestaurant(prev => ({ ...prev, phone: text }))}
            editable={isEditing}
            placeholder="Restaurant Phone"
            keyboardType="phone-pad"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Restaurant Email</Text>
          <TextInput
            style={[styles.input, !isEditing && styles.inputDisabled]}
            value={editedRestaurant.email}
            onChangeText={(text) => setEditedRestaurant(prev => ({ ...prev, email: text }))}
            editable={isEditing}
            placeholder="Restaurant Email"
            keyboardType="email-address"
          />
        </View>
      </View>

      {/* Restaurant Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Restaurant Status</Text>
        
        <View style={styles.switchGroup}>
          <View style={styles.switchInfo}>
            <Text style={styles.switchLabel}>Restaurant Open</Text>
            <Text style={styles.switchDescription}>
              Toggle to open/close your restaurant
            </Text>
          </View>
          <Switch
            value={editedRestaurant.isOpen}
            onValueChange={(value) => setEditedRestaurant(prev => ({ ...prev, isOpen: value }))}
            trackColor={{ false: Colors.light.border, true: Colors.light.success }}
            thumbColor={Colors.light.surface}
            disabled={!isEditing}
          />
        </View>

        <View style={styles.switchGroup}>
          <View style={styles.switchInfo}>
            <Text style={styles.switchLabel}>Accepting Orders</Text>
            <Text style={styles.switchDescription}>
              Toggle to accept/pause new orders
            </Text>
          </View>
          <Switch
            value={editedRestaurant.acceptingOrders}
            onValueChange={(value) => setEditedRestaurant(prev => ({ ...prev, acceptingOrders: value }))}
            trackColor={{ false: Colors.light.border, true: Colors.light.success }}
            thumbColor={Colors.light.surface}
            disabled={!isEditing}
          />
        </View>
      </View>

      {/* Account Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account</Text>
        
        <TouchableOpacity style={styles.actionButton}>
          <IconSymbol name="key" size={20} color={Colors.light.text} />
          <Text style={styles.actionButtonText}>Change Password</Text>
          <IconSymbol name="chevron.right" size={16} color={Colors.light.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <IconSymbol name="bell" size={20} color={Colors.light.text} />
          <Text style={styles.actionButtonText}>Notification Settings</Text>
          <IconSymbol name="chevron.right" size={16} color={Colors.light.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity style={[styles.actionButton, styles.logoutButton]} onPress={handleLogout}>
          <IconSymbol name="arrow.right.square" size={20} color={Colors.light.error} />
          <Text style={[styles.actionButtonText, styles.logoutButtonText]}>Logout</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  content: {
    paddingBottom: 40,
  },
  errorText: {
    fontSize: 16,
    color: Colors.light.error,
    textAlign: 'center',
    marginTop: 50,
  },
  header: {
    backgroundColor: Colors.light.primary,
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.textInverse,
    marginBottom: 4,
  },
  userRole: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 20,
  },
  headerActions: {
    width: '100%',
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    alignSelf: 'center',
  },
  editButtonText: {
    color: Colors.light.textInverse,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  cancelButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 20,
  },
  cancelButtonText: {
    color: Colors.light.textInverse,
    fontSize: 14,
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: Colors.light.textInverse,
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 20,
  },
  saveButtonText: {
    color: Colors.light.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    backgroundColor: Colors.light.surface,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 6,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.light.text,
  },
  inputDisabled: {
    backgroundColor: Colors.light.backgroundSecondary,
    color: Colors.light.textSecondary,
  },
  textArea: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.light.text,
    height: 80,
    textAlignVertical: 'top',
  },
  helperText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 4,
  },
  switchGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    marginBottom: 12,
  },
  switchInfo: {
    flex: 1,
    marginRight: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  switchDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  actionButtonText: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    marginLeft: 12,
  },
  logoutButton: {
    borderBottomWidth: 0,
  },
  logoutButtonText: {
    color: Colors.light.error,
  },
});
