import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Order, OrderFilters, OrderSortOptions, OrdersState, OrderStatus } from '../../types';

// Mock API calls - replace with actual API calls
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async (filters?: OrderFilters) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data - replace with actual API call
    const mockOrders: Order[] = [
      {
        id: '1',
        orderNumber: 'ORD-001',
        customer: {
          id: 'cust1',
          name: '<PERSON>',
          phone: '+1234567890',
          email: '<EMAIL>',
          address: {
            id: 'addr1',
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            coordinates: { latitude: 40.7128, longitude: -74.0060 }
          }
        },
        items: [],
        orderType: 'delivery',
        status: 'pending',
        paymentStatus: 'paid',
        subtotal: 25.99,
        tax: 2.60,
        deliveryFee: 3.99,
        tip: 5.00,
        discount: 0,
        total: 37.58,
        orderTime: new Date().toISOString(),
        estimatedPreparationTime: 30,
        paymentMethod: 'Credit Card'
      }
    ];
    
    return mockOrders;
  }
);

export const updateOrderStatus = createAsyncThunk(
  'orders/updateOrderStatus',
  async ({ orderId, status }: { orderId: string; status: OrderStatus }) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return { orderId, status };
  }
);

const initialState: OrdersState = {
  orders: [],
  loading: false,
  error: null,
  filters: {},
  sortOptions: {
    field: 'orderTime',
    direction: 'desc'
  }
};

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<OrderFilters>) => {
      state.filters = action.payload;
    },
    setSortOptions: (state, action: PayloadAction<OrderSortOptions>) => {
      state.sortOptions = action.payload;
    },
    addNewOrder: (state, action: PayloadAction<Order>) => {
      state.orders.unshift(action.payload);
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch orders';
      })
      .addCase(updateOrderStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        state.loading = false;
        const { orderId, status } = action.payload;
        const order = state.orders.find(o => o.id === orderId);
        if (order) {
          order.status = status;
          if (status === 'confirmed') {
            order.confirmedTime = new Date().toISOString();
          } else if (status === 'ready') {
            order.readyTime = new Date().toISOString();
          } else if (status === 'delivered') {
            order.deliveredTime = new Date().toISOString();
          }
        }
      })
      .addCase(updateOrderStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update order status';
      });
  }
});

export const { setFilters, setSortOptions, addNewOrder, clearError } = ordersSlice.actions;
export default ordersSlice.reducer;
