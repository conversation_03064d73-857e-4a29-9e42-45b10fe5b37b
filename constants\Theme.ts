import { StyleSheet } from 'react-native';

// Color Palette
export const Colors = {
  light: {
    // Primary Colors
    primary: '#FF6B35',      // Orange - main brand color
    primaryDark: '#E55A2B',  // Darker orange
    primaryLight: '#FF8A5C', // Lighter orange
    
    // Secondary Colors
    secondary: '#2E8B57',    // Sea green
    secondaryDark: '#1F5F3F', // Darker green
    secondaryLight: '#4CAF50', // Lighter green
    
    // Status Colors
    success: '#4CAF50',      // Green
    warning: '#FF9800',      // Amber
    error: '#F44336',        // Red
    info: '#2196F3',         // Blue
    
    // Background Colors
    background: '#FFFFFF',
    backgroundSecondary: '#F8F9FA',
    backgroundTertiary: '#F1F3F4',
    
    // Surface Colors
    surface: '#FFFFFF',
    surfaceSecondary: '#F5F5F5',
    
    // Text Colors
    text: '#212121',
    textSecondary: '#757575',
    textTertiary: '#9E9E9E',
    textInverse: '#FFFFFF',
    
    // Border Colors
    border: '#E0E0E0',
    borderLight: '#F0F0F0',
    borderDark: '#BDBDBD',
    
    // Shadow
    shadow: '#000000',
    
    // Order Status Colors
    pending: '#FF9800',      // Amber
    confirmed: '#2196F3',    // Blue
    preparing: '#9C27B0',    // Purple
    ready: '#4CAF50',        // Green
    delivered: '#4CAF50',    // Green
    cancelled: '#F44336',    // Red
    rejected: '#F44336',     // Red
  },
  dark: {
    // Primary Colors
    primary: '#FF6B35',
    primaryDark: '#E55A2B',
    primaryLight: '#FF8A5C',
    
    // Secondary Colors
    secondary: '#2E8B57',
    secondaryDark: '#1F5F3F',
    secondaryLight: '#4CAF50',
    
    // Status Colors
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
    
    // Background Colors
    background: '#121212',
    backgroundSecondary: '#1E1E1E',
    backgroundTertiary: '#2C2C2C',
    
    // Surface Colors
    surface: '#1E1E1E',
    surfaceSecondary: '#2C2C2C',
    
    // Text Colors
    text: '#FFFFFF',
    textSecondary: '#B3B3B3',
    textTertiary: '#808080',
    textInverse: '#000000',
    
    // Border Colors
    border: '#404040',
    borderLight: '#303030',
    borderDark: '#606060',
    
    // Shadow
    shadow: '#000000',
    
    // Order Status Colors
    pending: '#FF9800',
    confirmed: '#2196F3',
    preparing: '#9C27B0',
    ready: '#4CAF50',
    delivered: '#4CAF50',
    cancelled: '#F44336',
    rejected: '#F44336',
  }
};

// Typography
export const Typography = {
  // Font Sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Font Weights
  fontWeight: {
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  
  // Line Heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  }
};

// Spacing
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Border Radius
export const BorderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

// Shadows
export const Shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};

// Common Styles
export const CommonStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  column: {
    flexDirection: 'column',
  },
  
  card: {
    backgroundColor: Colors.light.surface,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    ...Shadows.small,
  },
  
  button: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  buttonPrimary: {
    backgroundColor: Colors.light.primary,
  },
  
  buttonSecondary: {
    backgroundColor: Colors.light.secondary,
  },
  
  buttonText: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.light.textInverse,
  },
  
  input: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: BorderRadius.sm,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    fontSize: Typography.fontSize.md,
    backgroundColor: Colors.light.surface,
  },
  
  textPrimary: {
    fontSize: Typography.fontSize.md,
    color: Colors.light.text,
    fontWeight: Typography.fontWeight.regular,
  },
  
  textSecondary: {
    fontSize: Typography.fontSize.sm,
    color: Colors.light.textSecondary,
    fontWeight: Typography.fontWeight.regular,
  },
  
  textBold: {
    fontWeight: Typography.fontWeight.bold,
  },
  
  textCenter: {
    textAlign: 'center',
  },
  
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: Spacing.md,
  },
  
  badge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  badgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.light.textInverse,
  },
});

// Order Status Styles
export const getOrderStatusColor = (status: string, isDark = false) => {
  const colors = isDark ? Colors.dark : Colors.light;
  
  switch (status) {
    case 'pending':
      return colors.pending;
    case 'confirmed':
      return colors.confirmed;
    case 'preparing':
      return colors.preparing;
    case 'ready':
      return colors.ready;
    case 'delivered':
      return colors.delivered;
    case 'cancelled':
    case 'rejected':
      return colors.cancelled;
    default:
      return colors.textSecondary;
  }
};

export const getOrderStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return 'Pending';
    case 'confirmed':
      return 'Confirmed';
    case 'preparing':
      return 'Preparing';
    case 'ready':
      return 'Ready';
    case 'picked_up':
      return 'Picked Up';
    case 'delivered':
      return 'Delivered';
    case 'cancelled':
      return 'Cancelled';
    case 'rejected':
      return 'Rejected';
    default:
      return status;
  }
};
