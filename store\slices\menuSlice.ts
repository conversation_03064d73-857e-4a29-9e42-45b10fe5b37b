import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { MenuItem, MenuCategory, MenuState } from '../../types';

export const fetchMenuItems = createAsyncThunk(
  'menu/fetchMenuItems',
  async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock menu data
    const mockCategories: MenuCategory[] = [
      { id: '1', name: 'Appetizers', description: 'Start your meal right', sortOrder: 1, isActive: true },
      { id: '2', name: 'Main Courses', description: 'Hearty and delicious', sortOrder: 2, isActive: true },
      { id: '3', name: 'Desserts', description: 'Sweet endings', sortOrder: 3, isActive: true },
      { id: '4', name: 'Beverages', description: 'Refreshing drinks', sortOrder: 4, isActive: true }
    ];
    
    const mockItems: MenuItem[] = [
      {
        id: '1',
        name: 'Caesar Salad',
        description: 'Fresh romaine lettuce with caesar dressing',
        price: 12.99,
        category: 'Appetizers',
        isAvailable: true,
        preparationTime: 10,
        isVegetarian: true,
        customizations: [
          {
            id: 'c1',
            name: 'Add Protein',
            type: 'single',
            required: false,
            options: [
              { id: 'o1', name: 'Grilled Chicken', price: 4.99 },
              { id: 'o2', name: 'Grilled Shrimp', price: 6.99 }
            ]
          }
        ]
      },
      {
        id: '2',
        name: 'Grilled Salmon',
        description: 'Fresh Atlantic salmon with herbs',
        price: 24.99,
        category: 'Main Courses',
        isAvailable: true,
        preparationTime: 25,
        customizations: [
          {
            id: 'c2',
            name: 'Cooking Style',
            type: 'single',
            required: true,
            options: [
              { id: 'o3', name: 'Medium', price: 0 },
              { id: 'o4', name: 'Well Done', price: 0 }
            ]
          }
        ]
      }
    ];
    
    return { categories: mockCategories, items: mockItems };
  }
);

export const updateMenuItemAvailability = createAsyncThunk(
  'menu/updateMenuItemAvailability',
  async ({ itemId, isAvailable }: { itemId: string; isAvailable: boolean }) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return { itemId, isAvailable };
  }
);

const initialState: MenuState = {
  categories: [],
  items: [],
  loading: false,
  error: null
};

const menuSlice = createSlice({
  name: 'menu',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addMenuItem: (state, action: PayloadAction<MenuItem>) => {
      state.items.push(action.payload);
    },
    updateMenuItem: (state, action: PayloadAction<MenuItem>) => {
      const index = state.items.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.items[index] = action.payload;
      }
    },
    removeMenuItem: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMenuItems.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMenuItems.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload.categories;
        state.items = action.payload.items;
      })
      .addCase(fetchMenuItems.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch menu items';
      })
      .addCase(updateMenuItemAvailability.fulfilled, (state, action) => {
        const { itemId, isAvailable } = action.payload;
        const item = state.items.find(item => item.id === itemId);
        if (item) {
          item.isAvailable = isAvailable;
        }
      });
  }
});

export const { clearError, addMenuItem, updateMenuItem, removeMenuItem } = menuSlice.actions;
export default menuSlice.reducer;
