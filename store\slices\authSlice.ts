import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  AuthState, 
  AuthCredentials, 
  RegisterData, 
  LoginResponse, 
  RegisterResponse, 
  User 
} from '@/types';

// Storage keys
const AUTH_TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_DATA_KEY = 'user_data';

// Mock API functions (replace with real API calls)
const mockApiDelay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const mockLogin = async (credentials: AuthCredentials): Promise<LoginResponse> => {
  await mockApiDelay(1500);
  
  // Mock validation
  if (credentials.email === '<EMAIL>' && credentials.password === 'password123') {
    return {
      user: {
        id: '1',
        email: credentials.email,
        firstName: '<PERSON>',
        lastName: 'Do<PERSON>',
        phone: '+1234567890',
        role: 'owner',
        isEmailVerified: true,
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      },
      token: 'mock_jwt_token_' + Date.now(),
      refreshToken: 'mock_refresh_token_' + Date.now(),
      restaurant: {
        id: '1',
        name: 'Demo Restaurant',
        description: 'A great place to eat',
        address: {
          street: '123 Main St',
          city: 'Demo City',
          state: 'DC',
          zipCode: '12345',
          country: 'USA'
        },
        phone: '+1234567890',
        email: credentials.email,
        isOpen: true,
        acceptingOrders: true,
        averagePreparationTime: 25,
        minimumOrderAmount: 15.00,
        deliveryFee: 3.99,
        operatingHours: {
          monday: { open: '09:00', close: '22:00', isOpen: true },
          tuesday: { open: '09:00', close: '22:00', isOpen: true },
          wednesday: { open: '09:00', close: '22:00', isOpen: true },
          thursday: { open: '09:00', close: '22:00', isOpen: true },
          friday: { open: '09:00', close: '23:00', isOpen: true },
          saturday: { open: '10:00', close: '23:00', isOpen: true },
          sunday: { open: '10:00', close: '21:00', isOpen: true },
        },
        rating: 4.8,
        totalReviews: 324,
        totalOrders: 1247,
        joinedDate: '2023-01-15',
        isVerified: true,
        cuisineType: 'American',
        priceRange: '$$'
      }
    };
  } else {
    throw new Error('Invalid email or password');
  }
};

const mockRegister = async (data: RegisterData): Promise<RegisterResponse> => {
  await mockApiDelay(2000);
  
  // Mock validation
  if (data.email === '<EMAIL>') {
    throw new Error('Email already exists');
  }
  
  return {
    user: {
      id: Date.now().toString(),
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      phone: data.phone,
      role: 'owner',
      isEmailVerified: false,
      createdAt: new Date().toISOString(),
    },
    token: 'mock_jwt_token_' + Date.now(),
    refreshToken: 'mock_refresh_token_' + Date.now(),
    restaurant: {
      id: Date.now().toString(),
      name: data.restaurantName,
      description: data.description,
      address: data.address,
      phone: data.phone,
      email: data.email,
      isOpen: false,
      acceptingOrders: false,
      averagePreparationTime: 30,
      minimumOrderAmount: 20.00,
      deliveryFee: 4.99,
      operatingHours: {
        monday: { open: '09:00', close: '22:00', isOpen: true },
        tuesday: { open: '09:00', close: '22:00', isOpen: true },
        wednesday: { open: '09:00', close: '22:00', isOpen: true },
        thursday: { open: '09:00', close: '22:00', isOpen: true },
        friday: { open: '09:00', close: '23:00', isOpen: true },
        saturday: { open: '10:00', close: '23:00', isOpen: true },
        sunday: { open: '10:00', close: '21:00', isOpen: true },
      },
      rating: 0,
      totalReviews: 0,
      totalOrders: 0,
      joinedDate: new Date().toISOString().split('T')[0],
      isVerified: false,
      cuisineType: data.restaurantType,
      priceRange: '$$'
    },
    requiresEmailVerification: true
  };
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials: AuthCredentials, { rejectWithValue }) => {
    try {
      const response = await mockLogin(credentials);
      
      // Store tokens and user data
      await AsyncStorage.setItem(AUTH_TOKEN_KEY, response.token);
      await AsyncStorage.setItem(REFRESH_TOKEN_KEY, response.refreshToken);
      await AsyncStorage.setItem(USER_DATA_KEY, JSON.stringify(response.user));
      
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Login failed');
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/register',
  async (data: RegisterData, { rejectWithValue }) => {
    try {
      const response = await mockRegister(data);
      
      // Store tokens and user data
      await AsyncStorage.setItem(AUTH_TOKEN_KEY, response.token);
      await AsyncStorage.setItem(REFRESH_TOKEN_KEY, response.refreshToken);
      await AsyncStorage.setItem(USER_DATA_KEY, JSON.stringify(response.user));
      
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Registration failed');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // Clear stored data
      await AsyncStorage.multiRemove([AUTH_TOKEN_KEY, REFRESH_TOKEN_KEY, USER_DATA_KEY]);
      return null;
    } catch (error) {
      return rejectWithValue('Logout failed');
    }
  }
);

export const loadStoredAuth = createAsyncThunk(
  'auth/loadStored',
  async (_, { rejectWithValue }) => {
    try {
      const [token, refreshToken, userData] = await AsyncStorage.multiGet([
        AUTH_TOKEN_KEY,
        REFRESH_TOKEN_KEY,
        USER_DATA_KEY
      ]);
      
      if (token[1] && userData[1]) {
        return {
          token: token[1],
          refreshToken: refreshToken[1],
          user: JSON.parse(userData[1]) as User
        };
      }
      
      return null;
    } catch (error) {
      return rejectWithValue('Failed to load stored authentication');
    }
  }
);

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  token: null,
  refreshToken: null,
};

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.error = action.payload as string;
      })
      // Register
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.error = action.payload as string;
      })
      // Logout
      .addCase(logoutUser.fulfilled, (state) => {
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.error = null;
        state.isLoading = false;
      })
      // Load stored auth
      .addCase(loadStoredAuth.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadStoredAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          state.isAuthenticated = true;
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.refreshToken = action.payload.refreshToken;
        }
      })
      .addCase(loadStoredAuth.rejected, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
      });
  },
});

export const { clearError, updateUser } = authSlice.actions;
export default authSlice.reducer;
