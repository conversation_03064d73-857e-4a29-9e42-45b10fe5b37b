import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchRestaurantInfo, updateRestaurantInfo } from '@/store/slices/restaurantSlice';
import { Restaurant } from '@/types';
import React, { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, Switch, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function SettingsScreen() {
  const dispatch = useAppDispatch();
  const { restaurant, loading } = useAppSelector(state => state.restaurant);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValues, setTempValues] = useState<Partial<Restaurant>>({});

  useEffect(() => {
    if (!restaurant) {
      dispatch(fetchRestaurantInfo());
    }
  }, [dispatch, restaurant]);

  const handleEdit = (field: string, currentValue: any) => {
    setEditingField(field);
    setTempValues({ [field]: currentValue });
  };

  const handleSave = (field: string) => {
    if (tempValues[field as keyof Restaurant] !== undefined) {
      dispatch(updateRestaurantInfo({ [field]: tempValues[field as keyof Restaurant] }));
      setEditingField(null);
      setTempValues({});
    }
  };

  const handleCancel = () => {
    setEditingField(null);
    setTempValues({});
  };

  const handleToggle = (field: string, value: boolean) => {
    dispatch(updateRestaurantInfo({ [field]: value }));
  };

  const formatOperatingHours = (hours: any[]) => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return hours.map(h => 
      `${days[h.dayOfWeek]}: ${h.isClosed ? 'Closed' : `${h.openTime} - ${h.closeTime}`}`
    ).join('\n');
  };

  const renderEditableField = (
    label: string,
    field: string,
    value: any,
    type: 'text' | 'number' = 'text',
    multiline = false
  ) => (
    <View style={styles.settingItem}>
      <Text style={styles.settingLabel}>{label}</Text>
      {editingField === field ? (
        <View style={styles.editContainer}>
          <TextInput
            style={[styles.input, multiline && styles.multilineInput]}
            value={String(tempValues[field as keyof Restaurant] || '')}
            onChangeText={(text) => setTempValues({ ...tempValues, [field]: type === 'number' ? parseFloat(text) || 0 : text })}
            keyboardType={type === 'number' ? 'numeric' : 'default'}
            multiline={multiline}
            autoFocus
          />
          <View style={styles.editActions}>
            <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveButton} onPress={() => handleSave(field)}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <TouchableOpacity style={styles.valueContainer} onPress={() => handleEdit(field, value)}>
          <Text style={styles.settingValue}>{String(value)}</Text>
          <IconSymbol name="pencil" size={16} color={Colors.light.textTertiary} />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderToggleField = (label: string, field: string, value: boolean, description?: string) => (
    <View style={styles.settingItem}>
      <View style={styles.toggleContainer}>
        <View>
          <Text style={styles.settingLabel}>{label}</Text>
          {description && <Text style={styles.settingDescription}>{description}</Text>}
        </View>
        <Switch
          value={value}
          onValueChange={(newValue) => handleToggle(field, newValue)}
          trackColor={{ false: Colors.light.border, true: Colors.light.success }}
          thumbColor={value ? Colors.light.textInverse : Colors.light.textTertiary}
        />
      </View>
    </View>
  );

  if (!restaurant) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading restaurant information...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Restaurant Settings</Text>
      </View>

      {/* Restaurant Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Restaurant Status</Text>
        {renderToggleField(
          'Restaurant Open',
          'isOpen',
          restaurant.isOpen,
          'Toggle to open/close your restaurant'
        )}
        {renderToggleField(
          'Accepting Orders',
          'acceptingOrders',
          restaurant.acceptingOrders,
          'Control whether to accept new orders'
        )}
      </View>

      {/* Basic Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Basic Information</Text>
        {renderEditableField('Restaurant Name', 'name', restaurant.name)}
        {renderEditableField('Description', 'description', restaurant.description, 'text', true)}
        {renderEditableField('Phone', 'phone', restaurant.phone)}
        {renderEditableField('Email', 'email', restaurant.email)}
        {renderEditableField('Website', 'website', restaurant.website || '')}
      </View>

      {/* Address */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Address</Text>
        {renderEditableField('Street', 'address.street', restaurant.address.street)}
        {renderEditableField('City', 'address.city', restaurant.address.city)}
        {renderEditableField('State', 'address.state', restaurant.address.state)}
        {renderEditableField('Zip Code', 'address.zipCode', restaurant.address.zipCode)}
      </View>

      {/* Operating Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Operating Settings</Text>
        {renderEditableField('Average Preparation Time (min)', 'averagePreparationTime', restaurant.averagePreparationTime, 'number')}
        {renderEditableField('Minimum Order Amount ($)', 'minimumOrderAmount', restaurant.minimumOrderAmount, 'number')}
        {renderEditableField('Delivery Fee ($)', 'deliveryFee', restaurant.deliveryFee, 'number')}
        {renderEditableField('Delivery Radius (km)', 'deliveryRadius', restaurant.deliveryRadius, 'number')}
      </View>

      {/* Operating Hours */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Operating Hours</Text>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Weekly Schedule</Text>
          <TouchableOpacity style={styles.hoursContainer}>
            <Text style={styles.hoursText}>
              {formatOperatingHours(restaurant.operatingHours)}
            </Text>
            <IconSymbol name="chevron.right" size={16} color={Colors.light.textTertiary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Statistics */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Statistics</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{restaurant.totalOrders}</Text>
            <Text style={styles.statLabel}>Total Orders</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{restaurant.rating.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Rating</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{restaurant.reviewCount}</Text>
            <Text style={styles.statLabel}>Reviews</Text>
          </View>
        </View>
      </View>

      {/* App Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Settings</Text>
        <TouchableOpacity style={styles.settingItem}>
          <View style={styles.settingRow}>
            <IconSymbol name="bell" size={20} color={Colors.light.textSecondary} />
            <Text style={styles.settingLabel}>Notifications</Text>
            <IconSymbol name="chevron.right" size={16} color={Colors.light.textTertiary} />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.settingItem}>
          <View style={styles.settingRow}>
            <IconSymbol name="questionmark.circle" size={20} color={Colors.light.textSecondary} />
            <Text style={styles.settingLabel}>Help & Support</Text>
            <IconSymbol name="chevron.right" size={16} color={Colors.light.textTertiary} />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.settingItem}>
          <View style={styles.settingRow}>
            <IconSymbol name="info.circle" size={20} color={Colors.light.textSecondary} />
            <Text style={styles.settingLabel}>About</Text>
            <IconSymbol name="chevron.right" size={16} color={Colors.light.textTertiary} />
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
  header: {
    padding: 20,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
  },
  section: {
    backgroundColor: Colors.light.surface,
    marginTop: 12,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  settingItem: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  settingLabel: {
    fontSize: 16,
    color: Colors.light.text,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 12,
    color: Colors.light.textTertiary,
  },
  settingValue: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editContainer: {
    marginTop: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    backgroundColor: Colors.light.background,
    marginBottom: 8,
  },
  multilineInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  editActions: {
    flexDirection: 'row',
    gap: 8,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  saveButtonText: {
    fontSize: 14,
    color: Colors.light.textInverse,
    fontWeight: '500',
  },
  hoursContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
  },
  hoursText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    flex: 1,
    lineHeight: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 16,
    borderRadius: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
});
