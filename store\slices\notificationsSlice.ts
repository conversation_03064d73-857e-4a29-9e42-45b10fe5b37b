import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Notification, NotificationsState } from '../../types';

export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock notifications
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'new_order',
        title: 'New Order Received',
        message: 'Order #ORD-001 from <PERSON>',
        timestamp: new Date(),
        isRead: false,
        orderId: '1'
      },
      {
        id: '2',
        type: 'system',
        title: 'System Update',
        message: 'App updated to version 2.1.0',
        timestamp: new Date(Date.now() - 3600000), // 1 hour ago
        isRead: true
      }
    ];
    
    return mockNotifications;
  }
);

const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null
};

const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<Notification>) => {
      state.notifications.unshift(action.payload);
      if (!action.payload.isRead) {
        state.unreadCount += 1;
      }
    },
    markAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification && !notification.isRead) {
        notification.isRead = true;
        state.unreadCount = Math.max(0, state.unreadCount - 1);
      }
    },
    markAllAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.isRead = true;
      });
      state.unreadCount = 0;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications = action.payload;
        state.unreadCount = action.payload.filter(n => !n.isRead).length;
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch notifications';
      });
  }
});

export const { addNotification, markAsRead, markAllAsRead, clearError } = notificationsSlice.actions;
export default notificationsSlice.reducer;
