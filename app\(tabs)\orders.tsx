import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors, getOrderStatusColor, getOrderStatusText } from '@/constants/Theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchOrders, updateOrderStatus } from '@/store/slices/ordersSlice';
import { Order, OrderStatus } from '@/types';
import { format } from 'date-fns';
import React, { useEffect, useState } from 'react';
import { Alert, FlatList, RefreshControl, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function OrdersScreen() {
  const dispatch = useAppDispatch();
  const { orders, loading } = useAppSelector(state => state.orders);
  const [selectedFilter, setSelectedFilter] = useState<OrderStatus | 'all'>('all');

  useEffect(() => {
    dispatch(fetchOrders());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchOrders());
  };

  const handleStatusUpdate = (orderId: string, newStatus: OrderStatus) => {
    Alert.alert(
      'Update Order Status',
      `Change order status to ${getOrderStatusText(newStatus)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Confirm', 
          onPress: () => dispatch(updateOrderStatus({ orderId, status: newStatus }))
        }
      ]
    );
  };

  const filteredOrders = orders.filter(order => 
    selectedFilter === 'all' || order.status === selectedFilter
  );

  const getStatusActions = (order: Order) => {
    switch (order.status) {
      case 'pending':
        return [
          { status: 'confirmed' as OrderStatus, label: 'Accept', color: Colors.light.success },
          { status: 'rejected' as OrderStatus, label: 'Reject', color: Colors.light.error }
        ];
      case 'confirmed':
        return [
          { status: 'preparing' as OrderStatus, label: 'Start Preparing', color: Colors.light.preparing }
        ];
      case 'preparing':
        return [
          { status: 'ready' as OrderStatus, label: 'Mark Ready', color: Colors.light.ready }
        ];
      default:
        return [];
    }
  };

  const renderOrderItem = ({ item }: { item: Order }) => (
    <View style={styles.orderCard}>
      <View style={styles.orderHeader}>
        <View>
          <Text style={styles.orderNumber}>#{item.orderNumber}</Text>
          <Text style={styles.customerName}>{item.customer.name}</Text>
          <Text style={styles.orderTime}>
            {format(new Date(item.orderTime), 'MMM dd, yyyy HH:mm')}
          </Text>
        </View>
        <View style={styles.orderMeta}>
          <View style={[styles.statusBadge, { backgroundColor: getOrderStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{getOrderStatusText(item.status)}</Text>
          </View>
          <Text style={styles.orderTotal}>${item.total.toFixed(2)}</Text>
        </View>
      </View>

      <View style={styles.orderDetails}>
        <View style={styles.orderInfo}>
          <IconSymbol 
            name={item.orderType === 'delivery' ? 'car.fill' : 'bag.fill'} 
            size={16} 
            color={Colors.light.textSecondary} 
          />
          <Text style={styles.orderType}>
            {item.orderType === 'delivery' ? 'Delivery' : 'Pickup'}
          </Text>
          {item.orderType === 'delivery' && item.deliveryAddress && (
            <Text style={styles.address} numberOfLines={1}>
              {item.deliveryAddress.street}, {item.deliveryAddress.city}
            </Text>
          )}
        </View>
        
        <Text style={styles.itemCount}>
          {item.items.length} item{item.items.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {item.specialInstructions && (
        <View style={styles.instructionsContainer}>
          <IconSymbol name="note.text" size={16} color={Colors.light.textSecondary} />
          <Text style={styles.instructions}>{item.specialInstructions}</Text>
        </View>
      )}

      <View style={styles.actionButtons}>
        {getStatusActions(item).map((action, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.actionButton, { backgroundColor: action.color }]}
            onPress={() => handleStatusUpdate(item.id, action.status)}
          >
            <Text style={styles.actionButtonText}>{action.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const filterOptions = [
    { key: 'all', label: 'All Orders' },
    { key: 'pending', label: 'Pending' },
    { key: 'confirmed', label: 'Confirmed' },
    { key: 'preparing', label: 'Preparing' },
    { key: 'ready', label: 'Ready' }
  ];

  return (
    <View style={styles.container}>
      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={filterOptions}
          keyExtractor={(item) => item.key}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterTab,
                selectedFilter === item.key && styles.activeFilterTab
              ]}
              onPress={() => setSelectedFilter(item.key as OrderStatus | 'all')}
            >
              <Text style={[
                styles.filterTabText,
                selectedFilter === item.key && styles.activeFilterTabText
              ]}>
                {item.label}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>

      {/* Orders List */}
      <FlatList
        data={filteredOrders}
        keyExtractor={(item) => item.id}
        renderItem={renderOrderItem}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={handleRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <IconSymbol name="tray" size={64} color={Colors.light.textTertiary} />
            <Text style={styles.emptyText}>No orders found</Text>
            <Text style={styles.emptySubtext}>
              {selectedFilter === 'all' 
                ? 'Orders will appear here when customers place them'
                : `No ${selectedFilter} orders at the moment`
              }
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  filterContainer: {
    backgroundColor: Colors.light.surface,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  activeFilterTab: {
    backgroundColor: Colors.light.primary,
  },
  filterTabText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: Colors.light.textInverse,
  },
  listContainer: {
    padding: 16,
  },
  orderCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 2,
  },
  customerName: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 2,
  },
  orderTime: {
    fontSize: 12,
    color: Colors.light.textTertiary,
  },
  orderMeta: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  orderTotal: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  orderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  orderType: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginLeft: 6,
    marginRight: 8,
  },
  address: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    flex: 1,
  },
  itemCount: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  instructionsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 8,
    borderRadius: 8,
    marginBottom: 12,
  },
  instructions: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginLeft: 6,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textInverse,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});
